# 动态 Cron 触发器设置指南

本文档详细说明如何配置和使用动态 Cron 触发器功能，该功能允许系统根据活跃任务自动优化调度频率。

## 功能概述

动态 Cron 触发器系统提供以下核心功能：

1. **智能调度优化** - 根据活跃任务数量和频率自动调整调度器触发频率
2. **实时触发器更新** - 通过 Cloudflare API 动态修改 Worker 的 Cron 触发器
3. **系统健康监控** - 基于系统负载和错误率调整调度策略
4. **手动触发器管理** - 提供 Web 界面和 API 进行手动触发器管理
5. **历史记录和监控** - 完整的更新历史和性能指标

## 前置要求

### 1. Cloudflare API Token

需要创建具有以下权限的 Cloudflare API Token：

- **Zone:Zone:Read** (如果需要访问域名信息)
- **Account:Cloudflare Workers:Edit** (必需)
- **Account:Account:Read** (必需)

创建步骤：
1. 登录 Cloudflare Dashboard
2. 进入 "My Profile" > "API Tokens"
3. 点击 "Create Token"
4. 选择 "Custom token"
5. 配置权限如上所述
6. 复制生成的 Token

### 2. Account ID

在 Cloudflare Dashboard 右侧边栏可以找到您的 Account ID。

## 配置步骤

### 1. 环境变量配置

在 `worker/.env` 文件中配置以下变量：

```bash
# 必需配置
CLOUDFLARE_API_TOKEN=your_api_token_here
ACCOUNT_ID=your_account_id_here
WORKER_NAME=cron-task-worker

# 动态 Cron 配置
ENABLE_DYNAMIC_CRON=true
CRON_UPDATE_MIN_INTERVAL=300000  # 5分钟最小间隔
```

### 2. wrangler.toml 配置

确保 `worker/wrangler.toml` 中的 Cron 触发器配置正确：

```toml
[triggers]
crons = ["*/5 * * * *", "*/5 * * * *", "0 */1 * * *"]
```

说明：
- 第一个触发器：主动态调度器（会被自动优化）
- 第二个触发器：备用调度器
- 第三个触发器：看门狗（每小时检查）

### 3. 部署配置

部署 Worker 时确保环境变量正确设置：

```bash
cd worker
wrangler deploy
```

## 使用方法

### 1. 自动优化

系统会在每次调度器运行时自动评估是否需要优化触发器：

- 分析当前活跃任务的调度模式
- 计算最优调度频率
- 考虑系统健康状况
- 在满足条件时自动更新触发器

### 2. 手动管理

#### Web 界面

访问前端应用的 "Cron管理" 页面：

1. **状态概览** - 查看当前触发器状态和配置
2. **优化建议** - 查看系统推荐的最优触发器
3. **手动更新** - 手动设置触发器表达式
4. **更新历史** - 查看所有触发器更新记录

#### API 接口

```bash
# 获取当前状态
GET /cron/status

# 获取优化建议
GET /cron/preview

# 手动更新触发器
POST /cron/update
{
  "triggers": ["*/3 * * * *", "*/5 * * * *", "0 */1 * * *"],
  "reason": "manual"
}

# 强制优化
POST /cron/optimize

# 验证配置
POST /cron/validate-config
```

### 3. 监控和调试

#### 日志监控

系统会记录所有触发器更新操作：

```bash
# 查看 Worker 日志
wrangler tail

# 查看特定的 Cron 更新日志
wrangler tail --format=pretty | grep "cron"
```

#### 性能指标

通过 API 获取性能指标：

```bash
GET /cron/metrics
```

返回数据包括：
- 更新成功率
- 系统负载指标
- 最近更新频率
- 触发器优化效果

## 故障排除

### 1. 常见问题

**问题：触发器更新失败**
```
Error: Failed to update cron triggers: HTTP 403: Forbidden
```

解决方案：
- 检查 API Token 权限
- 确认 Account ID 正确
- 验证 Worker 名称匹配

**问题：动态更新被跳过**
```
Cron update skipped: Rate limit: Too soon since last update
```

解决方案：
- 检查 `CRON_UPDATE_MIN_INTERVAL` 设置
- 等待足够的时间间隔
- 使用强制更新 API

**问题：系统健康检查失败**
```
Update validation failed: System health check failed
```

解决方案：
- 检查 KV 存储状态
- 查看 Durable Objects 健康状况
- 降低系统负载

### 2. 调试步骤

1. **验证配置**
   ```bash
   POST /cron/validate-config
   ```

2. **检查系统状态**
   ```bash
   GET /cron/status
   ```

3. **查看更新历史**
   ```bash
   GET /cron/history
   ```

4. **测试触发器验证**
   ```bash
   POST /cron/validate
   {
     "triggers": ["*/5 * * * *"]
   }
   ```

### 3. 性能优化

#### 调整更新频率

根据任务负载调整最小更新间隔：

```bash
# 高负载环境（减少更新频率）
CRON_UPDATE_MIN_INTERVAL=600000  # 10分钟

# 低负载环境（增加更新频率）
CRON_UPDATE_MIN_INTERVAL=180000  # 3分钟
```

#### 优化触发器策略

根据任务模式调整触发器：

```bash
# 高频任务场景
crons = ["*/1 * * * *", "*/2 * * * *", "*/5 * * * *"]

# 低频任务场景
crons = ["*/10 * * * *", "*/15 * * * *", "0 */1 * * *"]
```

## 安全考虑

1. **API Token 安全**
   - 使用最小权限原则
   - 定期轮换 Token
   - 不要在代码中硬编码 Token

2. **访问控制**
   - 确保 Cron 管理 API 需要认证
   - 限制管理员访问权限
   - 记录所有手动更新操作

3. **监控和告警**
   - 设置触发器更新失败告警
   - 监控异常的更新频率
   - 跟踪系统性能指标

## 最佳实践

1. **渐进式部署**
   - 先在测试环境验证配置
   - 逐步启用动态优化功能
   - 监控系统行为变化

2. **备份策略**
   - 保留原始触发器配置
   - 定期备份触发器设置
   - 准备回滚方案

3. **性能监控**
   - 定期检查优化效果
   - 分析任务执行延迟
   - 调整优化算法参数

4. **文档维护**
   - 记录配置变更
   - 更新操作手册
   - 培训运维团队

## 支持和反馈

如果遇到问题或需要功能改进，请：

1. 查看系统日志和错误信息
2. 使用调试 API 收集详细信息
3. 参考故障排除指南
4. 联系技术支持团队

---

更新时间：2025-01-02
版本：1.0.0
