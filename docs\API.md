# Cron Task Manager API Documentation

## Base URL

- Development: `http://localhost:8787/api`
- Production: `https://your-worker.your-subdomain.workers.dev/api`

## Authentication

All API endpoints except `/auth/login` and `/auth/status` require authentication using JWT tokens.

### Headers

```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Authentication Endpoints

### POST /auth/login

Login with password to get JW<PERSON> token.

**Request Body:**
```json
{
  "password": "your_password"
}
```

**Response:**
```json
{
  "success": true,
  "token": "jwt_token_here",
  "expiresIn": "24h",
  "user": {
    "id": "admin",
    "role": "admin"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### POST /auth/verify

Verify current JWT token.

**Response:**
```json
{
  "valid": true,
  "user": {
    "id": "admin",
    "role": "admin"
  },
  "expiresAt": "2024-01-02T00:00:00.000Z",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### POST /auth/logout

Logout user (mainly for client-side cleanup).

**Response:**
```json
{
  "success": true,
  "message": "Logged out successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /auth/status

Get authentication system status.

**Response:**
```json
{
  "authenticationEnabled": true,
  "rateLimitingEnabled": true,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Task Endpoints

### GET /tasks

Get all tasks.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "task-uuid",
      "name": "Example Task",
      "description": "Task description",
      "type": "http_request",
      "status": "active",
      "cron": "0 */1 * * *",
      "timezone": "UTC",
      "config": {
        "url": "https://example.com",
        "method": "GET"
      },
      "retryConfig": {
        "enabled": true,
        "maxRetries": 3,
        "strategy": "exponential",
        "baseDelay": 1000,
        "maxDelay": 60000
      },
      "timeout": 30000,
      "tags": ["example"],
      "metadata": {},
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "lastRunAt": "2024-01-01T01:00:00.000Z",
      "nextRunAt": "2024-01-01T02:00:00.000Z",
      "executorName": "HTTP Request",
      "executorIcon": "Promotion"
    }
  ],
  "count": 1,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /tasks/:id

Get specific task by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "task-uuid",
    "name": "Example Task",
    // ... other task properties
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### POST /tasks

Create new task.

**Request Body:**
```json
{
  "name": "New Task",
  "description": "Task description",
  "type": "http_request",
  "status": "active",
  "cron": "0 */1 * * *",
  "timezone": "UTC",
  "config": {
    "url": "https://example.com",
    "method": "GET",
    "headers": {},
    "body": ""
  },
  "retryConfig": {
    "enabled": true,
    "maxRetries": 3,
    "strategy": "exponential",
    "baseDelay": 1000,
    "maxDelay": 60000
  },
  "timeout": 30000,
  "tags": ["example"],
  "metadata": {}
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "generated-uuid",
    // ... complete task object
  },
  "message": "Task created successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### PUT /tasks/:id

Update existing task.

**Request Body:** Same as POST /tasks

**Response:**
```json
{
  "success": true,
  "data": {
    // ... updated task object
  },
  "message": "Task updated successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### DELETE /tasks/:id

Delete task.

**Response:**
```json
{
  "success": true,
  "message": "Task deleted successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### POST /tasks/:id/run

Manually trigger task execution.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "executed",
    "timestamp": "2024-01-01T00:00:00.000Z"
  },
  "message": "Task execution triggered successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /tasks/types

Get available task types and their configurations.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "type": "http_request",
      "name": "HTTP Request",
      "description": "Sends HTTP requests to specified URLs",
      "icon": "Promotion",
      "configSchema": {
        // JSON schema for configuration
      }
    }
  ],
  "count": 1,
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Dashboard Endpoints

### GET /dashboard

Get dashboard overview data.

**Response:**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalTasks": 10,
      "activeTasks": 8,
      "inactiveTasks": 2,
      "pausedTasks": 0,
      "healthScore": 95,
      "lastUpdate": "2024-01-01T00:00:00.000Z"
    },
    "tasksByType": {
      "http_request": 5,
      "webhook": 3,
      "email": 2
    },
    "upcomingTasks": [
      {
        "id": "task-uuid",
        "name": "Task Name",
        "type": "http_request",
        "nextRunAt": "2024-01-01T01:00:00.000Z",
        "timeUntilRun": 3600000
      }
    ],
    "recentFailures": [
      {
        "id": "log-uuid",
        "taskId": "task-uuid",
        "taskName": "Failed Task",
        "message": "Error message",
        "timestamp": "2024-01-01T00:00:00.000Z",
        "retryCount": 1
      }
    ],
    "systemStatus": {
      "health": {
        "checks": {
          "kv_storage": "healthy",
          "durable_objects": "healthy"
        }
      },
      "lastSchedulerRun": "2024-01-01T00:00:00.000Z",
      "uptime": 86400000,
      "schedulerRuns": 1440
    },
    "alerts": [
      {
        "type": "info",
        "title": "System Status",
        "message": "All systems operational",
        "timestamp": "2024-01-01T00:00:00.000Z"
      }
    ]
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /dashboard/stats

Get detailed statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "taskStats": {
      // Task statistics
    },
    "logStats": {
      "lastHour": { "total": 10, "failures": 1, "successes": 9 },
      "last6Hours": { "total": 60, "failures": 5, "successes": 55 },
      "last24Hours": { "total": 240, "failures": 12, "successes": 228 },
      "lastWeek": { "total": 1680, "failures": 84, "successes": 1596 }
    },
    "successRates": {
      "lastHour": "90.00",
      "last6Hours": "91.67",
      "last24Hours": "95.00",
      "lastWeek": "95.00"
    },
    "timestamp": "2024-01-01T00:00:00.000Z"
  }
}
```

## Log Endpoints

### GET /logs

Get logs with optional filtering.

**Query Parameters:**
- `limit` (number): Maximum number of logs to return (1-100, default: 50)
- `type` (string): Filter by log type ('failure', 'success', 'all')
- `taskId` (string): Filter by specific task ID

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "log-uuid",
      "taskId": "task-uuid",
      "taskName": "Task Name",
      "type": "task_failure",
      "level": "error",
      "status": "failure",
      "message": "Task execution failed: Error message",
      "details": {
        "error": {
          "name": "Error",
          "message": "Error message",
          "stack": "Error stack trace"
        }
      },
      "executionTime": 5000,
      "retryCount": 1,
      "timestamp": "2024-01-01T00:00:00.000Z",
      "metadata": {},
      "timeAgo": "5 minutes ago",
      "severity": "high"
    }
  ],
  "count": 1,
  "filters": {
    "limit": 50,
    "type": "all",
    "taskId": null
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /logs/:id

Get specific log entry.

**Response:**
```json
{
  "success": true,
  "data": {
    // ... complete log entry
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /logs/stats

Get log statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "total": 100,
    "byStatus": {
      "success": 85,
      "failure": 12,
      "timeout": 3
    },
    "byLevel": {
      "info": 85,
      "error": 12,
      "warn": 3
    },
    "byType": {
      "task_execution": 85,
      "task_failure": 15
    },
    "byTimeRange": {
      "lastHour": 10,
      "last6Hours": 60,
      "last24Hours": 100,
      "lastWeek": 700
    },
    "topFailingTasks": [
      {
        "taskId": "task-uuid",
        "taskName": "Failing Task",
        "failureCount": 5
      }
    ],
    "averageExecutionTime": 2500
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### GET /logs/export

Export logs in JSON or CSV format.

**Query Parameters:**
- `format` (string): Export format ('json' or 'csv', default: 'json')
- `limit` (number): Maximum number of logs to export (1-1000, default: 100)

**Response:**
- JSON format: Same as GET /logs
- CSV format: CSV file download

### DELETE /logs

Clear all logs (admin only).

**Response:**
```json
{
  "success": true,
  "message": "All logs cleared successfully",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "error": "Error Type",
  "message": "Detailed error message",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "path": "/api/endpoint",
  "method": "GET"
}
```

### Common HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid or missing token)
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting for authentication endpoints:

- Login endpoint: 5 attempts per 15 minutes per IP
- Other endpoints: Standard rate limiting applies

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time
