{"name": "cron-task-frontend", "version": "1.0.0", "description": "Frontend for cron task management system", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts --fix", "format": "prettier --write src/**/*.{vue,js,ts,css,scss}"}, "dependencies": {"@element-plus/icons-vue": "^2.3.0", "@tanstack/vue-query": "^5.0.0", "axios": "^1.6.0", "cron-parser": "^4.9.0", "dayjs": "^1.11.0", "element-plus": "^2.4.0", "js-yaml": "^4.1.0", "pinia": "^2.1.0", "vue": "^3.4.0", "vue-i18n": "^9.14.5", "vue-router": "^4.2.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.0", "prettier": "^3.2.0", "sass": "^1.69.0", "unplugin-auto-import": "^0.17.0", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.0"}, "keywords": ["vue3", "cron", "task-scheduler", "element-plus", "cloudflare"], "author": "", "license": "MIT"}