# 🚀 纯自调度模型快速部署指南

## 📋 部署前检查清单

- [ ] Cloudflare账号（免费版即可）
- [ ] wrangler CLI已安装（`npm install -g wrangler`）
- [ ] 已登录wrangler（`wrangler login`）
- [ ] 项目代码已下载到本地

## ⚡ 5分钟快速部署

### 步骤1：环境准备
```bash
# 进入worker目录
cd worker

# 检查wrangler登录状态
wrangler whoami

# 安装依赖（如果需要）
npm install
```

### 步骤2：创建KV命名空间
```bash
# 创建生产环境KV命名空间
wrangler kv:namespace create TASKS_KV

# 复制输出的ID，更新wrangler.toml中的id字段
# 示例输出：
# 🌀 Creating namespace with title "self-scheduling-tasks-TASKS_KV"
# ✨ Success! Created KV namespace with id "your-kv-namespace-id"
```

### 步骤3：更新配置
编辑 `wrangler.toml`，更新KV命名空间ID：
```toml
[[kv_namespaces]]
binding = "TASKS_KV"
id = "your-kv-namespace-id"  # 替换为步骤2中获得的ID
```

### 步骤4：部署到生产环境
```bash
# 部署Worker
wrangler deploy

# 记录部署后的URL，格式类似：
# https://self-scheduling-tasks.your-subdomain.workers.dev
```

### 步骤5：验证部署
```bash
# 健康检查（替换为你的实际URL）
curl "https://self-scheduling-tasks.your-subdomain.workers.dev/"

# 预期响应：
# {
#   "message": "Self-Scheduling Tasks Worker is running",
#   "model": "Pure Self-Scheduling (One Task One DO)",
#   "timestamp": "2025-08-04T...",
#   "version": "2.0.0"
# }
```

## 🔧 创建第一个任务

### HTTP任务示例
```bash
# 创建HTTP任务配置
wrangler kv:key put "task_config:my-first-http-task" \
  '{
    "type": "http",
    "url": "https://httpbin.org/get",
    "method": "GET",
    "cron": "0 * * * *",
    "enabled": true,
    "timeout": 30000
  }' \
  --binding TASKS_KV

# 测试任务
curl "https://your-worker-url.workers.dev/health?taskId=my-first-http-task"

# 手动触发任务
curl -X POST "https://your-worker-url.workers.dev/manual-trigger?taskId=my-first-http-task"
```

### Webhook任务示例
```bash
# 首先在 https://webhook.site 创建一个测试URL

# 创建Webhook任务配置
wrangler kv:key put "task_config:my-first-webhook-task" \
  '{
    "type": "webhook",
    "webhookUrl": "https://webhook.site/your-unique-url",
    "cron": "*/5 * * * *",
    "enabled": true,
    "payload": {
      "message": "Hello from self-scheduling task!",
      "timestamp": "auto-generated"
    }
  }' \
  --binding TASKS_KV

# 测试Webhook任务
curl -X POST "https://your-worker-url.workers.dev/manual-trigger?taskId=my-first-webhook-task"
```

## 📊 监控和管理

### 查看任务状态
```bash
# 健康检查
curl "https://your-worker-url.workers.dev/health?taskId=your-task-id"

# 获取详细统计
curl "https://your-worker-url.workers.dev/stats?taskId=your-task-id"

# 查看实时日志
wrangler tail
```

### 更新任务配置
```bash
# 方法1：通过API更新
curl -X POST "https://your-worker-url.workers.dev/config?taskId=your-task-id" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "http",
    "url": "https://api.example.com/updated-endpoint",
    "method": "POST",
    "cron": "0 */2 * * *",
    "enabled": true,
    "timeout": 45000
  }'

# 方法2：通过KV直接更新
wrangler kv:key put "task_config:your-task-id" \
  '{"type":"http","url":"https://new-url.com","cron":"0 */2 * * *","enabled":true}' \
  --binding TASKS_KV
```

## 🎯 常用Cron表达式

| 描述 | Cron表达式 | 说明 |
|------|------------|------|
| 每分钟 | `* * * * *` | 每分钟执行一次 |
| 每5分钟 | `*/5 * * * *` | 每5分钟执行一次 |
| 每小时 | `0 * * * *` | 每小时的0分执行 |
| 每2小时 | `0 */2 * * *` | 每2小时的0分执行 |
| 每天上午9点 | `0 9 * * *` | 每天上午9:00执行 |
| 每周一上午9点 | `0 9 * * 1` | 每周一上午9:00执行 |
| 工作日上午9点 | `0 9 * * 1-5` | 周一到周五上午9:00执行 |

## 🔍 故障排除

### 常见问题

#### 1. 任务未执行
```bash
# 检查任务配置
wrangler kv:key get "task_config:your-task-id" --binding TASKS_KV

# 检查任务状态
curl "https://your-worker-url.workers.dev/health?taskId=your-task-id"

# 手动触发测试
curl -X POST "https://your-worker-url.workers.dev/manual-trigger?taskId=your-task-id"
```

#### 2. 配置错误
```bash
# 验证配置格式
echo '{"type":"http","url":"https://example.com","cron":"0 * * * *","enabled":true}' | jq .

# 检查URL可访问性
curl -I "https://your-target-url.com"
```

#### 3. 权限问题
```bash
# 检查wrangler权限
wrangler whoami

# 重新登录
wrangler logout
wrangler login
```

### 日志分析
```bash
# 查看实时日志
wrangler tail --format pretty

# 过滤特定任务的日志
wrangler tail --format pretty | grep "your-task-id"

# 查看错误日志
wrangler tail --format pretty | grep -i error
```

## 📈 扩展和优化

### 批量创建任务
```bash
# 创建批量任务脚本
cat > create-tasks.sh << 'EOF'
#!/bin/bash
for i in {1..10}; do
  wrangler kv:key put "task_config:batch-task-$i" \
    "{\"type\":\"http\",\"url\":\"https://httpbin.org/get?id=$i\",\"method\":\"GET\",\"cron\":\"$((i*5)) * * * *\",\"enabled\":true}" \
    --binding TASKS_KV
  echo "Created task: batch-task-$i"
done
EOF

chmod +x create-tasks.sh
./create-tasks.sh
```

### 监控脚本
```bash
# 创建监控脚本
cat > monitor-tasks.sh << 'EOF'
#!/bin/bash
WORKER_URL="https://your-worker-url.workers.dev"
TASKS=("task-1" "task-2" "task-3")

echo "=== 任务健康监控 ==="
echo "时间: $(date)"
echo ""

for task in "${TASKS[@]}"; do
  echo "检查任务: $task"
  health=$(curl -s "$WORKER_URL/health?taskId=$task")
  status=$(echo $health | jq -r '.status // "unknown"')
  echo "  状态: $status"
  
  if [ "$status" != "healthy" ]; then
    echo "  ⚠️  警告: 任务状态异常"
  fi
  echo ""
done
EOF

chmod +x monitor-tasks.sh
./monitor-tasks.sh
```

## 🎉 部署完成

恭喜！你已经成功部署了纯自调度模型任务系统。

### 下一步建议：
1. 创建几个测试任务验证功能
2. 设置监控脚本定期检查任务状态
3. 根据实际需求调整任务配置
4. 查看[改造完成总结.md](./改造完成总结.md)了解更多技术细节

### 获取帮助：
- 查看实时日志：`wrangler tail`
- 检查任务状态：访问 `/health?taskId=your-task-id`
- 手动触发任务：POST 到 `/manual-trigger?taskId=your-task-id`

---

**部署指南版本：** 1.0  
**最后更新：** 2025年8月4日  
**适用版本：** 纯自调度模型 v2.0.0
